{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:clean": "vite --force", "build": "vite build", "build:with-check": "vue-tsc && vite build", "preview": "vite preview", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vueuse/core": "^13.4.0", "axios": "^1.6.7", "echarts": "^5.6.0", "element-plus": "^2.5.3", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "papaparse": "^5.4.1", "pinia": "^2.1.7", "vant": "^4.9.20", "vue": "^3.4.15", "vue-router": "^4.2.5", "vue-tsc": "^2.1.10", "vuedraggable": "^4.1.0"}, "devDependencies": {"@stagewise/toolbar-vue": "^0.4.9", "@types/file-saver": "^2.0.7", "@types/node": "^24.0.4", "@types/papaparse": "^5.3.14", "@vant/auto-import-resolver": "^1.3.0", "@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "sass": "^1.83.0", "typescript": "^5.3.3", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.7.0", "vite": "^7.0.0"}}