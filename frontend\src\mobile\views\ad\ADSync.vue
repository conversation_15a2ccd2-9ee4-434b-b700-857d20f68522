<template>
  <div class="mobile-ad-sync">
    <van-cell-group title="同步状态" inset>
      <van-cell
        title="上次同步"
        value="2分钟前"
        label="同步了128个用户"
      />
      <van-cell
        title="同步状态"
        :value="syncStatus"
        :label="syncProgress"
      >
        <template #icon>
          <van-icon 
            :name="syncStatus === '空闲' ? 'success' : 'loading'" 
            :color="syncStatus === '空闲' ? '#67c23a' : '#409eff'"
            size="16"
          />
        </template>
      </van-cell>
    </van-cell-group>

    <van-cell-group title="同步设置" inset>
      <van-cell
        title="自动同步"
        center
      >
        <template #right-icon>
          <van-switch v-model="autoSync" />
        </template>
      </van-cell>
      <van-field
        v-model="syncInterval"
        label="同步间隔"
        placeholder="分钟"
        type="number"
        suffix="分钟"
      />
    </van-cell-group>

    <div class="sync-actions">
      <van-button 
        type="primary" 
        block 
        :loading="syncing"
        @click="startSync"
      >
        {{ syncing ? '同步中...' : '立即同步' }}
      </van-button>
    </div>

    <van-cell-group title="同步日志" inset>
      <van-cell
        v-for="log in syncLogs"
        :key="log.id"
        :title="log.message"
        :label="log.time"
        :value="log.status"
      />
    </van-cell-group>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { showToast } from 'vant'

const syncStatus = ref('空闲')
const syncProgress = ref('')
const syncing = ref(false)
const autoSync = ref(true)
const syncInterval = ref('30')

const syncLogs = ref([
  {
    id: 1,
    message: 'AD用户同步完成',
    time: '2分钟前',
    status: '成功'
  },
  {
    id: 2,
    message: 'AD组织结构同步',
    time: '1小时前',
    status: '成功'
  },
  {
    id: 3,
    message: '增量同步完成',
    time: '3小时前',
    status: '成功'
  }
])

const startSync = async () => {
  syncing.value = true
  syncStatus.value = '同步中'
  syncProgress.value = '正在同步用户信息...'
  
  try {
    // 模拟同步过程
    await new Promise(resolve => setTimeout(resolve, 3000))
    
    // 添加同步日志
    syncLogs.value.unshift({
      id: Date.now(),
      message: 'AD手动同步完成',
      time: '刚刚',
      status: '成功'
    })
    
    showToast('同步完成')
  } catch (error) {
    showToast('同步失败')
  } finally {
    syncing.value = false
    syncStatus.value = '空闲'
    syncProgress.value = ''
  }
}
</script>

<style lang="scss" scoped>
@use '@mobile/styles/variables.scss' as vars;

.mobile-ad-sync {
  padding: vars.$padding-md;
  background-color: #f7f8fa;
  min-height: 100vh;
}

.van-cell-group {
  margin-bottom: vars.$padding-lg;
}

.sync-actions {
  margin: vars.$padding-lg 0;
}
</style> 