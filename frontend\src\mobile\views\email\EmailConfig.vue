<template>
  <div class="mobile-email-config">
    <van-cell-group title="邮箱配置" inset>
      <van-cell
        title="邮箱服务商"
        value="腾讯企业邮箱"
        label="当前使用的邮箱服务"
      />
      <van-cell
        title="API状态"
        value="正常"
        label="API连接正常"
      >
        <template #icon>
          <van-icon name="success" color="#67c23a" size="16" />
        </template>
      </van-cell>
    </van-cell-group>

    <van-cell-group title="快速操作" inset>
      <van-cell
        title="创建邮箱账号"
        icon="add-o"
        is-link
        @click="showToast('功能开发中...')"
      />
      <van-cell
        title="同步邮箱成员"
        icon="synchronous"
        is-link
        @click="showToast('功能开发中...')"
      />
    </van-cell-group>
  </div>
</template>

<script setup lang="ts">
import { showToast } from 'vant'
</script>

<style lang="scss" scoped>
@use '@mobile/styles/variables.scss' as vars;

.mobile-email-config {
  padding: vars.$padding-md;
  background-color: #f7f8fa;
  min-height: 100vh;
}

.van-cell-group {
  margin-bottom: vars.$padding-lg;
}
</style> 