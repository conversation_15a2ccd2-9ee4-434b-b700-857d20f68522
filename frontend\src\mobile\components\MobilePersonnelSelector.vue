<template>
  <div class="mobile-personnel-selector">
    <!-- 人员输入框 -->
    <van-field
      :model-value="displayValue"
      :name="name"
      :label="label"
      :placeholder="placeholder"
      :rules="rules"
      readonly
      is-link
      @click="showSelector = true"
    >
      <template #right-icon>
        <van-icon name="arrow-down" />
      </template>
    </van-field>

    <!-- 选择器弹窗 -->
    <van-popup 
      v-model:show="showSelector" 
      position="bottom" 
      :style="{ height: 'var(--mobile-popup-medium-height, 58svh)' }"
      round
      closeable
      close-icon-position="top-right"
    >
      <div class="selector-container">
        <!-- 标题栏 -->
        <div class="selector-header">
          <h3>{{ selectorTitle }}</h3>
        </div>

        <!-- 搜索框 -->
        <div class="search-section">
          <van-search
            v-model="searchKeyword"
            placeholder="输入姓名或工号进行搜索"
            @search="handleSearch"
            @clear="handleClear"
            show-action
            @cancel="handleClear"
          />
        </div>

        <!-- 人员列表 -->
        <div class="personnel-section">
          <van-list
            v-model:loading="loading"
            :finished="finished"
            finished-text="没有更多数据了"
            @load="onLoad"
          >
            <!-- 人员选项列表 -->
            <van-cell
              v-for="person in personnelList"
              :key="person.id || person.UserID"
              :title="person.user_name || person.UserName"
              is-link
              @click="handleSelect(person)"
              :class="{ 'selected-option': selectedPersonId === (person.id || person.UserID) }"
            >
              <template #label>
                <div class="personnel-info">
                  <div v-if="person.job_number || person.JobNumber" class="job-number"
                       :class="{ 'highlight-match': isJobNumberMatch(person) }">
                    工号：{{ person.job_number || person.JobNumber }}
                    <van-tag v-if="isJobNumberMatch(person)" type="primary" class="match-tag">
                      匹配
                    </van-tag>
                  </div>
                  <div v-if="person.dept_name || person.DeptName" class="department">
                    部门：{{ person.dept_name || person.DeptName }}
                  </div>
                  <div v-if="person.job_title_name || person.JobTitleName" class="job-title">
                    职位：{{ person.job_title_name || person.JobTitleName }}
                  </div>
                </div>
              </template>

              <template #right-icon>
                <van-icon 
                  v-if="selectedPersonId === (person.id || person.UserID)" 
                  name="success" 
                  color="#07c160" 
                />
              </template>
            </van-cell>

            <!-- 空状态 -->
            <div v-if="personnelList.length === 0 && !loading" class="empty-state">
              <van-empty description="暂无人员数据" />
            </div>
          </van-list>
        </div>

        <!-- 底部操作栏 -->
        <MobilePopupFooter :buttons="selectorFooterButtons" />
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { showToast } from 'vant'
import { ecologyApi } from '@/api/ecology'
import type { EcologyUser } from '@/types/ecology'
import MobilePopupFooter from './MobilePopupFooter.vue'

interface Props {
  modelValue?: string
  name?: string
  label?: string
  placeholder?: string
  rules?: any[]
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  name: '',
  label: '人员',
  placeholder: '请选择人员',
  rules: () => [],
  disabled: false
})

const emit = defineEmits<{
  'update:modelValue': [value: string]
  'change': [value: string, person?: EcologyUser]
  'select': [person: EcologyUser]
}>()

// 响应式数据
const showSelector = ref(false)
const searchKeyword = ref('')
const selectedPersonId = ref<number | null>(null)
const selectedPerson = ref<EcologyUser | null>(null)
const personnelList = ref<EcologyUser[]>([])
const loading = ref(false)
const finished = ref(true) // 远程搜索模式，默认完成状态
const searchTimeout = ref<ReturnType<typeof setTimeout> | null>(null)

// 计算属性
const displayValue = computed(() => props.modelValue)

const selectorTitle = computed(() => {
  return `选择${props.label}`
})

// 检查是否为工号匹配
const isJobNumberMatch = (person: EcologyUser) => {
  if (!searchKeyword.value || !/\d/.test(searchKeyword.value)) return false
  const jobNumber = (person.job_number || person.JobNumber || '').toLowerCase()
  return jobNumber.includes(searchKeyword.value.toLowerCase())
}

// 获取人员数据
const fetchPersonnelData = async (keyword = '', isNewSearch = false) => {
  if (isNewSearch) {
    personnelList.value = []
    finished.value = false
  }

  try {
    loading.value = true
    
    // 优化工号搜索逻辑：
    // 1. 如果是纯数字且长度>=3，优先使用精确匹配
    // 2. 其他情况使用模糊匹配
    const isLikelyJobNumber = /^\d{3,}$/.test(keyword)
    
    const response = await ecologyApi.getLocalEcologyUsers({
      skip: 0,
      limit: 50,
      keyword: keyword,
      exact_match: isLikelyJobNumber && keyword.length >= 4 // 4位以上数字才精确匹配
    })

    // 只显示有用户名和用户ID的记录，并去重
    let filteredData = response.data.filter(user => 
      (user.user_name || user.UserName) && (user.user_id || user.UserID)
    )
    
    // 根据用户ID或工号去重，保留第一个匹配的记录
    const seenKeys = new Set()
    let newData = filteredData.filter(user => {
      const userId = user.user_id || user.UserID
      const jobNumber = user.job_number || user.JobNumber
      
      // 优先使用用户ID，其次使用工号
      const key = userId || jobNumber
      if (!key || seenKeys.has(key)) {
        return false
      }
      seenKeys.add(key)
      return true
    })
    
    // 如果是数字搜索但精确匹配没有结果，尝试模糊匹配
    if (isLikelyJobNumber && newData.length === 0 && keyword.length >= 3) {
      const fuzzyResponse = await ecologyApi.getLocalEcologyUsers({
        skip: 0,
        limit: 50,
        keyword: keyword,
        exact_match: false
      })
      
      let fuzzyFilteredData = fuzzyResponse.data.filter(user => 
        (user.user_name || user.UserName) && (user.user_id || user.UserID)
      )
      
      // 对降级搜索结果也进行去重
      const fuzzySeenKeys = new Set()
      newData = fuzzyFilteredData.filter(user => {
        const userId = user.user_id || user.UserID
        const jobNumber = user.job_number || user.JobNumber
        
        const key = userId || jobNumber
        if (!key || fuzzySeenKeys.has(key)) {
          return false
        }
        fuzzySeenKeys.add(key)
        return true
      })
    }
    
    // 优化排序：工号匹配的结果优先显示
    if (keyword && /\d/.test(keyword)) {
      newData.sort((a, b) => {
        const aJobNumber = (a.job_number || a.JobNumber || '').toLowerCase()
        const bJobNumber = (b.job_number || b.JobNumber || '').toLowerCase()
        const searchLower = keyword.toLowerCase()
        
        const aJobMatch = aJobNumber.includes(searchLower)
        const bJobMatch = bJobNumber.includes(searchLower)
        
        if (aJobMatch && !bJobMatch) return -1
        if (!aJobMatch && bJobMatch) return 1
        
        // 都匹配或都不匹配时，按姓名排序
        const aName = (a.user_name || a.UserName || '').toLowerCase()
        const bName = (b.user_name || b.UserName || '').toLowerCase()
        return aName.localeCompare(bName)
      })
    }
    
    personnelList.value = newData
    finished.value = true
  } catch (error) {
    console.error('获取人员数据失败:', error)
    showToast('获取人员数据失败')
  } finally {
    loading.value = false
  }
}

// 加载初始数据
const onLoad = async () => {
  if (!searchKeyword.value) {
    // 如果没有搜索关键词，加载默认的少量数据
    await fetchPersonnelData('', true)
  }
}

// 搜索处理（防抖）
const handleSearch = () => {
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value)
  }
  
  searchTimeout.value = setTimeout(() => {
    fetchPersonnelData(searchKeyword.value, true)
  }, 300)
}

// 清除搜索
const handleClear = () => {
  searchKeyword.value = ''
  fetchPersonnelData('', true)
}

// 选择人员
const handleSelect = (person: EcologyUser) => {
  selectedPersonId.value = person.id || person.UserID || null
  selectedPerson.value = person
}

// 确认选择
const handleConfirm = () => {
  if (selectedPerson.value) {
    const personName = selectedPerson.value.user_name || selectedPerson.value.UserName || ''
    emit('update:modelValue', personName)
    emit('change', personName, selectedPerson.value)
    emit('select', selectedPerson.value)
  } else {
    emit('update:modelValue', '')
    emit('change', '')
  }
  
  showSelector.value = false
}

// 底部按钮配置
const selectorFooterButtons = computed(() => [
  {
    text: '取消',
    type: 'default' as const,
    onClick: () => {
      showSelector.value = false
    }
  },
  {
    text: '确定',
    type: 'primary' as const,
    onClick: handleConfirm
  }
])

// 监听搜索关键词变化
watch(searchKeyword, () => {
  if (searchKeyword.value.length >= 1) {
    handleSearch()
  } else {
    handleClear()
  }
})

// 监听modelValue变化，尝试匹配已有人员
watch(() => props.modelValue, (newValue) => {
  if (newValue && personnelList.value.length > 0) {
    const person = personnelList.value.find(p => 
      (p.user_name === newValue || p.UserName === newValue)
    )
    if (person) {
      selectedPersonId.value = person.id || person.UserID || null
      selectedPerson.value = person
    }
  } else if (!newValue) {
    selectedPersonId.value = null
    selectedPerson.value = null
  }
})

// 组件挂载时加载初始数据
onMounted(() => {
  fetchPersonnelData('', true)
})
</script>

<style scoped>
.mobile-personnel-selector {
  width: 100%;
}

.selector-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f7f8fa;
}

.selector-header {
  padding: 16px;
  background: white;
  border-bottom: 1px solid #ebedf0;
  text-align: center;
}

.selector-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #323233;
}

.search-section {
  background: white;
  padding-bottom: 8px;
}

.personnel-section {
  flex: 1;
  overflow: hidden;
  background: white;
  margin-top: 8px;
}

.personnel-info {
  margin-top: 4px;
}

.personnel-info > div {
  font-size: 12px;
  color: #969799;
  line-height: 16px;
}

.job-number {
  color: #1989fa;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
}

.job-number.highlight-match {
  background-color: #fff7e6;
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid #ffd591;
}

.match-tag {
  font-size: 10px !important;
  padding: 1px 4px !important;
  height: 16px !important;
  line-height: 14px !important;
}

.selected-option {
  background: #f0f9ff;
}

.selected-option :deep(.van-cell__title) {
  color: #1989fa;
  font-weight: 500;
}

.empty-state {
  padding: 40px 20px;
  text-align: center;
}


</style> 